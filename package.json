{"name": "mnf-front", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^20.0.0", "@angular/cdk": "^20.0.4", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/localize": "^20.0.0", "@angular/material": "^20.0.4", "@angular/platform-browser": "^20.0.0", "@angular/router": "^20.0.0", "axios": "^1.10.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "^0.15.1"}, "devDependencies": {"@angular/build": "^20.0.3", "@angular/cli": "^20.0.3", "@angular/compiler-cli": "^20.0.0", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.16", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "~5.8.2"}}