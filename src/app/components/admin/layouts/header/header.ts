import { Component, Input, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MaterialModules } from '../../../../material/material';
import { LanguageService, Language } from '../../../../services/language.service';
import { TranslatePipe } from '../../../../pipes/translate.pipe';
import { ThemeToggleComponent } from '../../../shared/theme-toggle/theme-toggle';
import { MatSidenav } from '@angular/material/sidenav';
import { AuthService } from '../../../../services/auth.service';

@Component({
  selector: 'app-admin-header',
  imports: [RouterLink, MaterialModules, TranslatePipe, CommonModule, ThemeToggleComponent],
  templateUrl: './header.html',
  standalone: true,
  styleUrl: './header.css'
})
export class Header implements OnInit, AfterViewInit {
  @ViewChild('sidenav') sidenav!: MatSidenav;
  sidenavOpened = false;
  languages: Language[] = [];
  currentLanguage: Language;
  isDarkMode = true; // Default to dark mode for admin

  // Admin navigation links
  navLinks = [
    { label: 'Promotions', path: '/admin/promotions' },
    { label: 'Interns', path: '/admin/interns' },
    { label: 'Chapters', path: '/admin/chapters' },
    { label: 'Questions', path: '/admin/questions' },
    { label: 'Answers', path: '/admin/answers' }
  ];

  constructor(
    private authService: AuthService,
    private languageService: LanguageService
  ) {
    this.currentLanguage = this.languageService.getCurrentLanguage();
  }

  ngOnInit() {
    this.languages = this.languageService.getLanguages();
  }

  ngAfterViewInit() {
    // Add a slight delay to ensure the sidenav is properly initialized
    setTimeout(() => {
      // Sidenav should be initialized if needed
    }, 100);
  }

  toggleSidenav() {
    // Simply toggle the sidenavOpened property
    this.sidenavOpened = !this.sidenavOpened;

    // If the sidenav is now open, we need to initialize it after the view is updated
    if (this.sidenavOpened) {
      // Use setTimeout to ensure the sidenav is rendered before trying to access it
      setTimeout(() => {
        // Sidenav should be initialized if needed
      }, 100);
    }
  }

  changeLanguage(langCode: string) {
    this.languageService.setLanguage(langCode);
  }

  onThemeChanged(isDarkMode: boolean): void {
    this.isDarkMode = isDarkMode;
    // Apply theme to the admin layout
    document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');
  }

  logout(): void {
    this.authService.logout();
  }
}
