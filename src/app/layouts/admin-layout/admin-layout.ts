import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { Header } from '../../components/admin/layouts/header/header';
import { Footer } from '../../components/admin/layouts/footer/footer';

@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [RouterOutlet, Header, Footer],
  templateUrl: './admin-layout.html',
})
export class AdminLayout {
  // The logout functionality has been moved to the Header component
}
