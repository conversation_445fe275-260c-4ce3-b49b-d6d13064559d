/* Global layout styles */
:host {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Main content styles */
.main-content {
  flex: 1;
  min-height: calc(100vh - 128px); /* Ensure content takes up at least the viewport height minus header and footer */
}

/* Default content wrapper for most pages */
.main-content > *:not(app-home) {
  padding: 16px; /* Reduced top padding since header is now sticky, not fixed */
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

/* Home page gets full width */
.main-content app-home {
  display: block;
  width: 100%;
}

/* Responsive styles */
@media (max-width: 768px) {
  .main-content {
    padding: 12px; /* Reduced padding for mobile */
  }
}
