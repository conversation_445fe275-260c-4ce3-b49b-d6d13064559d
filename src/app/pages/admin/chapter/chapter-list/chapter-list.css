.chapter-list-container {
  padding: 1rem;
}

/* Add custom styles for the table */
table {
  border-collapse: separate;
  border-spacing: 0;
}

th, td {
  border-bottom-width: 1px;
}

/* Style for action buttons */
button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: inherit;
  padding: 0;
}

/* Animation for the loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
