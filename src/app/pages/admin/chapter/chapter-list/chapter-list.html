<div class="chapter-list-container">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold" i18n="@@chaptersTitle">Chapters</h1>
    <a routerLink="/admin/chapters/create" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200" i18n="@@createChapterButton">
      Create Chapter
    </a>
  </div>

  <!-- Error message -->
  @if (error) {
    <div class="bg-red-800 text-white p-4 rounded mb-4">
      {{ error }}
    </div>
  }

  <!-- Loading indicator -->
  @if (loading) {
    <div class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      <span class="ml-3" i18n="@@loadingChapters">Loading chapters...</span>
    </div>
  }

  <!-- Chapters list -->
  @if (!loading && chapters.length > 0) {
    <div class="bg-gray-800 rounded-lg shadow overflow-hidden">
      <table class="min-w-full divide-y divide-gray-700">
        <thead class="bg-gray-700">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" i18n="@@idColumn">ID</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" i18n="@@titleColumn">Title</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" i18n="@@pathColumn">Path</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" i18n="@@actionsColumn">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-gray-800 divide-y divide-gray-700">
          @for (chapter of chapters; track chapter.id) {
            <tr class="hover:bg-gray-700 transition-colors duration-150">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ chapter.id }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ chapter.title }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ chapter.path }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  @if (chapter.id !== undefined) {
                    <a [routerLink]="['/admin/chapters', chapter.id]" class="text-blue-400 hover:text-blue-300" i18n="@@viewButton">View</a>
                  }
                  @if (chapter.id !== undefined) {
                    <a [routerLink]="['/admin/chapters/edit', chapter.id]" class="text-yellow-400 hover:text-yellow-300" i18n="@@editButton">Edit</a>
                  }
                  @if (chapter.id !== undefined) {
                    <button (click)="deleteChapter(chapter.id)" class="text-red-400 hover:text-red-300" i18n="@@deleteButton">Delete</button>
                  }
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }

  <!-- No chapters message -->
  @if (!loading && chapters.length === 0) {
    <div class="bg-gray-800 rounded-lg p-8 text-center">
      <p class="text-xl text-gray-300" i18n="@@noChaptersMessage">No chapters found. Create your first chapter to get started.</p>
    </div>
  }
</div>
