<div class="intern-form">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-2xl font-bold">{{ isEdit ? 'Edit' : 'Create' }} Intern</h2>
    <a routerLink="/admin/interns" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
      Back to List
    </a>
  </div>

  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    {{ error }}
  </div>

  <div *ngIf="success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
    {{ success }}
  </div>

  <div *ngIf="loading" class="text-center py-4">
    <p>Loading...</p>
  </div>

  <form *ngIf="!loading" (ngSubmit)="saveIntern()" #internForm="ngForm" class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
    <div class="mb-4">
      <label class="block text-gray-700 text-sm font-bold mb-2" for="first_name">
        First Name
      </label>
      <input
        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
        id="first_name"
        type="text"
        name="first_name"
        [(ngModel)]="intern.first_name"
        required
        #firstName="ngModel"
        placeholder="First name">
      <div *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)" class="text-red-500 text-xs italic">
        First name is required
      </div>
    </div>

    <div class="mb-4">
      <label class="block text-gray-700 text-sm font-bold mb-2" for="last_name">
        Last Name
      </label>
      <input
        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
        id="last_name"
        type="text"
        name="last_name"
        [(ngModel)]="intern.last_name"
        required
        #lastName="ngModel"
        placeholder="Last name">
      <div *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)" class="text-red-500 text-xs italic">
        Last name is required
      </div>
    </div>

    <div class="mb-4">
      <label class="block text-gray-700 text-sm font-bold mb-2" for="arrival">
        Arrival Date
      </label>
      <input
        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
        id="arrival"
        type="date"
        name="arrival"
        [ngModel]="intern.arrival | date:'yyyy-MM-dd'"
        (ngModelChange)="intern.arrival = $event"
        placeholder="Arrival date">
    </div>

    <div class="mb-4">
      <label class="block text-gray-700 text-sm font-bold mb-2" for="formation_over">
        Formation Over Date
      </label>
      <input
        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
        id="formation_over"
        type="date"
        name="formation_over"
        [ngModel]="intern.formation_over | date:'yyyy-MM-dd'"
        (ngModelChange)="intern.formation_over = $event"
        placeholder="Formation over date">
    </div>

    <div class="mb-4">
      <label class="block text-gray-700 text-sm font-bold mb-2" for="promotion_id">
        Promotion
      </label>
      <select
        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
        id="promotion_id"
        name="promotion_id"
        [(ngModel)]="intern.promotion_id">
        <option [ngValue]="null">None</option>
        <option *ngFor="let promotion of promotions" [ngValue]="promotion.id">{{ promotion.name }}</option>
      </select>
    </div>

    <div class="flex items-center justify-between">
      <button
        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        type="submit"
        [disabled]="internForm.invalid || loading">
        {{ isEdit ? 'Update' : 'Create' }} Intern
      </button>
    </div>
  </form>
</div>
