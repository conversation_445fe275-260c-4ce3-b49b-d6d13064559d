<div class="promotion-list">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-2xl font-bold">Promotions</h2>
    <a routerLink="/admin/promotions/create" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
      Add Promotion
    </a>
  </div>

  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    {{ error }}
  </div>

  <div *ngIf="loading" class="text-center py-4">
    <p>Loading promotions...</p>
  </div>

  <div *ngIf="!loading && promotions.length === 0" class="text-center py-4">
    <p>No promotions found.</p>
  </div>

  <div *ngIf="!loading && promotions.length > 0" class="overflow-x-auto">
    <table class="min-w-full bg-white border border-gray-200">
      <thead>
        <tr>
          <th class="py-2 px-4 border-b text-left">ID</th>
          <th class="py-2 px-4 border-b text-left">Name</th>
          <th class="py-2 px-4 border-b text-right">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let promotion of promotions" class="hover:bg-gray-50">
          <td class="py-2 px-4 border-b">{{ promotion.id }}</td>
          <td class="py-2 px-4 border-b">{{ promotion.name }}</td>
          <td class="py-2 px-4 border-b text-right">
            <a [routerLink]="['/admin/promotions', promotion.id]" class="text-blue-500 hover:text-blue-700 mr-2">View</a>
            <a [routerLink]="['/admin/promotions/edit', promotion.id]" class="text-green-500 hover:text-green-700 mr-2">Edit</a>
            <button (click)="deletePromotion(promotion.id!)" class="text-red-500 hover:text-red-700">Delete</button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
