import { Component, OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Promotion } from '../../../../models/admin/Promotion';
import { PromotionService } from '../../../../services/admin/promotion.service';

@Component({
  selector: 'app-promotion-list',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './promotion-list.html',
})
export class PromotionListComponent implements OnInit {
  promotions: Promotion[] = [];
  loading = true;
  error = '';

  constructor(private promotionService: PromotionService) {}

  ngOnInit(): void {
    this.loadPromotions();
  }

  loadPromotions(): void {
    this.loading = true;
    this.promotionService.getPromotions()
      .then(promotions => {
        this.promotions = promotions;
        this.loading = false;
      })
      .catch(error => {
        this.error = 'Failed to load promotions. Please try again.';
        this.loading = false;
        console.error('Error loading promotions:', error);
      });
  }

  deletePromotion(id: number): void {
    if (confirm('Are you sure you want to delete this promotion?')) {
      this.promotionService.deletePromotion(id)
        .then(success => {
          if (success) {
            this.promotions = this.promotions.filter(p => p.id !== id);
          } else {
            this.error = 'Failed to delete promotion. Please try again.';
          }
        })
        .catch(error => {
          this.error = 'Failed to delete promotion. Please try again.';
          console.error('Error deleting promotion:', error);
        });
    }
  }
}
