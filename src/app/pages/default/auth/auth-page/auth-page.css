/* Custom styles for auth page component */
:host {
  display: block;
}

/* Styling for the auth container */
.auth-toggle {
  margin-bottom: 1rem;
}

/* Custom styling for the tabs */
::ng-deep .auth-tabs .mat-mdc-tab-header {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1rem;
  background-color: #f3f4f6;
}

::ng-deep .auth-tabs .mat-mdc-tab {
  min-width: 120px;
  transition: all 0.3s ease;
}

::ng-deep .auth-tabs .mat-mdc-tab-body-content {
  overflow: hidden;
  padding: 0.5rem 0;
}

/* Custom styling for form fields */
::ng-deep .mat-mdc-form-field {
  width: 100%;
}

::ng-deep .mat-mdc-form-field.mat-form-field-appearance-outline .mat-form-field-outline {
  background-color: #f9fafb;
  border-radius: 8px;
}

::ng-deep .mat-mdc-form-field.mat-form-field-appearance-outline .mat-form-field-flex {
  padding: 0.5rem 0.75rem;
}

::ng-deep .mat-mdc-form-field.mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.5rem 0;
}

/* Remove the vertical divider in the middle of the form fields */
::ng-deep .mat-mdc-form-field.mat-form-field-appearance-outline .mat-form-field-outline-gap {
  border-top-color: transparent !important;
}

/* Add some responsive adjustments */
@media (max-width: 640px) {
  .max-w-md {
    max-width: 95%;
    margin: 0 auto;
  }
}
