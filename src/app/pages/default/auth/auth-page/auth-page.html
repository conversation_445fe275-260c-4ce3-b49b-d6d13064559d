<div class="flex justify-center items-center min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
  <div class="w-full max-w-md p-8 bg-white rounded-xl shadow-xl transform transition-all duration-300 ease-in-out">
    <div class="text-center mb-6">
      <h1 class="text-3xl font-bold text-gray-800 mb-2">{{ 'auth.welcome' | translate }}</h1>
      <p class="text-gray-600">
        {{ 'auth.signInOrCreate' | translate }}
      </p>
    </div>

    <div class="flex justify-center mb-6">
      <mat-slide-toggle
        [checked]="!showLogin"
        (change)="toggleForm()"
        color="primary"
        class="auth-toggle"
      >
        <span class="text-sm font-medium">{{ (showLogin ? 'auth.createAccount' : 'auth.login') | translate }}</span>
      </mat-slide-toggle>
    </div>

    <mat-tab-group
      [selectedIndex]="showLogin ? 0 : 1"
      (selectedIndexChange)="tabChanged($event)"
      animationDuration="300ms"
      class="auth-tabs"
    >
      <mat-tab label="{{ 'auth.login' | translate }}">
        <div class="py-4">
          @if (showLogin) {
            <app-login></app-login>
          }
        </div>
      </mat-tab>
      <mat-tab label="{{ 'auth.register' | translate }}">
        <div class="py-4">
          @if (!showLogin) {
            <app-register></app-register>
          }
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
