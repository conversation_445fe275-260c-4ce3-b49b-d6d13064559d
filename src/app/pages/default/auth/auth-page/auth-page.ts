import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaterialModules } from '../../../../material/material';
import { LoginPage } from '../../../../components/default/auth/login/login';
import { RegisterPage } from '../../../../components/default/auth/register/register';
import { trigger, transition, style, animate } from '@angular/animations';
import { TranslatePipe } from '../../../../pipes/translate.pipe';

@Component({
  selector: 'app-auth-page',
  standalone: true,
  imports: [CommonModule, MaterialModules, LoginPage, RegisterPage, TranslatePipe],
  templateUrl: './auth-page.html',
  styleUrl: './auth-page.css',
  animations: [
    trigger('formAnimation', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(10px)' }),
        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ]),
      transition(':leave', [
        animate('300ms ease-in', style({ opacity: 0, transform: 'translateY(10px)' }))
      ])
    ])
  ]
})
export class AuthPage {
  showLogin = true; // Default to showing login form

  toggleForm() {
    this.showLogin = !this.showLogin;
  }

  tabChanged(index: number) {
    this.showLogin = index === 0;
  }
}
