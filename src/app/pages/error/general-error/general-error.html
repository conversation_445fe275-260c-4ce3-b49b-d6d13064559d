<div class="error-container">
  <mat-card class="error-card">
    <mat-card-header>
      <mat-card-title>
        <h1 class="error-title">{{ 'error.general.title' | translate }}</h1>
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="error-icon">
        <mat-icon>error</mat-icon>
      </div>
      <p class="error-code">{{ 'error.code' | translate }}: {{ errorCode }}</p>
      <p class="error-message">{{ 'error.general.message' | translate }}</p>
    </mat-card-content>
    <mat-card-actions>
      <a mat-raised-button color="primary" routerLink="/">
        {{ 'error.backToHome' | translate }}
      </a>
    </mat-card-actions>
  </mat-card>
</div>
