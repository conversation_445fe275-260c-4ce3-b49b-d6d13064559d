import { Component } from '@angular/core';
import { MatToolbarModule } from '@angular/material/toolbar';
import { TranslatePipe } from '../../../pipes/translate.pipe';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-home',
  imports: [MatToolbarModule, TranslatePipe, CommonModule],
  templateUrl: './home.html',
  standalone: true,
  styleUrl: './home.css'
})
export class HomePage {

  constructor(private router: Router) {}

  /**
   * Navigate to join quiz page
   */
  joinQuiz(): void {
    // TODO: Navigate to join quiz page when implemented
    console.log('Join quiz clicked');
  }

  /**
   * Navigate to create quiz page
   */
  createQuiz(): void {
    // TODO: Navigate to create quiz page when implemented
    console.log('Create quiz clicked');
  }
}

