/* Custom styles for the home page */
.home-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Animation for the illustration */
.illustration-container img {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Button hover effects */
.btn-primary {
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
}

/* Responsive text sizing */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1rem;
  }
}

/* Custom colors to match the design */
.text-quiz-pink {
  color: #ec4899;
}

.bg-quiz-teal {
  background-color: #14b8a6;
}

.bg-quiz-pink {
  background-color: #ec4899;
}