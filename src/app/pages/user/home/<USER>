/* Custom styles for the home page */
.home-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.home-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(236, 72, 153, 0.05) 0%, transparent 50%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Animation for the main illustration */
.illustration-float {
  animation: float 4s ease-in-out infinite;
}

.illustration-float-delayed {
  animation: float 4s ease-in-out infinite;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-8px) rotate(1deg);
  }
  50% {
    transform: translateY(-15px) rotate(0deg);
  }
  75% {
    transform: translateY(-8px) rotate(-1deg);
  }
}

/* Enhanced button styles */
button {
  position: relative;
  overflow: hidden;
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

button:hover::before {
  left: 100%;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .home-container {
    padding: 2rem 1rem;
  }
}

@media (max-width: 768px) {
  .home-container h1 {
    font-size: 2.5rem !important;
    line-height: 1.2;
  }

  .home-container p {
    font-size: 1rem !important;
  }

  .home-container .space-y-8 {
    gap: 1.5rem;
  }

  /* Stack illustrations vertically on mobile */
  .home-container .relative .absolute {
    position: relative !important;
    margin-top: 1rem;
  }
}

@media (max-width: 640px) {
  .home-container .flex-col {
    gap: 0.75rem;
  }

  .home-container button {
    padding: 1rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* Custom animations for decorative elements */
@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.delay-1000 {
  animation-delay: 1s;
}

.delay-2000 {
  animation-delay: 2s;
}

/* Text highlighting effects */
.text-pink-500 {
  position: relative;
  display: inline-block;
}

.text-pink-500::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #ec4899, #f472b6);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.text-pink-500:hover::after {
  transform: scaleX(1);
}