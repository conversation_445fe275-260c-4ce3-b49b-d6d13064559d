<div class="home-container min-h-screen bg-gray-50 flex items-center justify-center px-4 py-8">
  <div class="max-w-7xl w-full">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <!-- Left side - Content -->
      <div class="space-y-8 lg:pr-8">
        <!-- Welcome text -->
        <div class="space-y-4">
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 leading-tight">
            {{ 'home.welcome' | translate }}
            <span class="text-pink-500">{{ 'home.appName' | translate }}</span> !
          </h1>
        </div>

        <!-- Description -->
        <div class="text-lg md:text-xl text-gray-600 leading-relaxed space-y-3">
          <p>
            {{ 'home.description' | translate }}
            <span class="text-pink-500 font-medium">{{ 'home.learningEffective' | translate }}</span>{{ 'home.aloneOrGroup' | translate }}
            <span class="text-pink-500 font-medium">{{ 'home.group' | translate }}</span>.
          </p>
          <p>
            <span class="text-pink-500 font-medium">{{ 'home.createQuiz' | translate }}</span>
            {{ 'home.or' | translate }}
            <span class="text-pink-500 font-medium">{{ 'home.joinQuiz' | translate }}</span>
            {{ 'home.withCode' | translate }}
            <span class="text-pink-500 font-medium">{{ 'home.startNow' | translate }}</span> !
          </p>
        </div>

        <!-- Action buttons -->
        <div class="flex flex-col sm:flex-row gap-4 pt-6">
          <button
            (click)="joinQuiz()"
            class="bg-teal-500 hover:bg-teal-600 text-white font-semibold py-4 px-8 rounded-xl transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
            {{ 'home.joinQuizBtn' | translate }}
          </button>
          <button
            (click)="createQuiz()"
            class="bg-pink-500 hover:bg-pink-600 text-white font-semibold py-4 px-8 rounded-xl transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
            {{ 'home.createQuizBtn' | translate }}
          </button>
        </div>
      </div>

      <!-- Right side - Illustrations -->
      <div class="relative flex justify-center lg:justify-end">
        <div class="relative w-full max-w-lg">
          <!-- Main illustration (person with lightbulb) -->
          <div class="relative z-10">
            <img
              src="assets/images/image_home_page_1.svg"
              alt="Quiz Learning Illustration"
              class="w-full h-auto illustration-float"
            />
          </div>

          <!-- Secondary illustration (person with laptop) -->
          <div class="absolute bottom-0 right-0 w-3/5 z-20">
            <img
              src="assets/images/image_home_page_2.svg"
              alt="Learning with laptop"
              class="w-full h-auto illustration-float-delayed"
            />
          </div>

          <!-- Decorative elements -->
          <div class="absolute top-10 left-10 w-4 h-4 bg-pink-300 rounded-full opacity-60 animate-pulse"></div>
          <div class="absolute top-32 right-8 w-3 h-3 bg-teal-300 rounded-full opacity-60 animate-pulse delay-1000"></div>
          <div class="absolute bottom-20 left-4 w-2 h-2 bg-purple-300 rounded-full opacity-60 animate-pulse delay-2000"></div>
        </div>
      </div>
    </div>
  </div>
</div>
