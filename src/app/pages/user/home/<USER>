<div class="p-8 bg-gray-100 rounded-lg shadow-md">
  <h1 class="text-3xl font-bold text-blue-600 mb-4">{{ 'app.welcome' | translate }}</h1>
  <div class="flex flex-col md:flex-row gap-4">
    <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-300">
      {{ 'home.getStarted' | translate }}
    </button>
    <button class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition duration-300">
      {{ 'home.learnMore' | translate }}
    </button>
  </div>
  <div class="mt-8 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
    <!-- Card 1 -->
    <div class="bg-white p-4 rounded shadow hover:shadow-lg transition duration-300">
      <h2 class="text-xl font-semibold text-gray-800">{{ 'home.feature1.title' | translate }}</h2>
      <p class="text-gray-600">{{ 'home.feature1.description' | translate }}</p>
    </div>
    <!-- Card 2 -->
    <div class="bg-white p-4 rounded shadow hover:shadow-lg transition duration-300">
      <h2 class="text-xl font-semibold text-gray-800">{{ 'home.feature2.title' | translate }}</h2>
      <p class="text-gray-600">{{ 'home.feature2.description' | translate }}</p>
    </div>
    <!-- Card 3 -->
    <div class="bg-white p-4 rounded shadow hover:shadow-lg transition duration-300">
      <h2 class="text-xl font-semibold text-gray-800">{{ 'home.feature3.title' | translate }}</h2>
      <p class="text-gray-600">{{ 'home.feature3.description' | translate }}</p>
    </div>
    <!-- Card 4 -->
    <div class="bg-white p-4 rounded shadow hover:shadow-lg transition duration-300">
      <h2 class="text-xl font-semibold text-gray-800">{{ 'home.feature1.title' | translate }}</h2>
      <p class="text-gray-600">{{ 'home.feature1.description' | translate }}</p>
    </div>
    <!-- Card 5 -->
    <div class="bg-white p-4 rounded shadow hover:shadow-lg transition duration-300">
      <h2 class="text-xl font-semibold text-gray-800">{{ 'home.feature2.title' | translate }}</h2>
      <p class="text-gray-600">{{ 'home.feature2.description' | translate }}</p>
    </div>
    <!-- Card 6 -->
    <div class="bg-white p-4 rounded shadow hover:shadow-lg transition duration-300">
      <h2 class="text-xl font-semibold text-gray-800">{{ 'home.feature3.title' | translate }}</h2>
      <p class="text-gray-600">{{ 'home.feature3.description' | translate }}</p>
    </div>
    <!-- Card 7 -->
    <div class="bg-white p-4 rounded shadow hover:shadow-lg transition duration-300">
      <h2 class="text-xl font-semibold text-gray-800">{{ 'home.feature1.title' | translate }}</h2>
      <p class="text-gray-600">{{ 'home.feature1.description' | translate }}</p>
    </div>
    <!-- Card 8 -->
    <div class="bg-white p-4 rounded shadow hover:shadow-lg transition duration-300">
      <h2 class="text-xl font-semibold text-gray-800">{{ 'home.feature2.title' | translate }}</h2>
      <p class="text-gray-600">{{ 'home.feature2.description' | translate }}</p>
    </div>
    <!-- Card 9 -->
    <div class="bg-white p-4 rounded shadow hover:shadow-lg transition duration-300">
      <h2 class="text-xl font-semibold text-gray-800">{{ 'home.feature3.title' | translate }}</h2>
      <p class="text-gray-600">{{ 'home.feature3.description' | translate }}</p>
    </div>
    <!-- Card 10 -->
    <div class="bg-white p-4 rounded shadow hover:shadow-lg transition duration-300">
      <h2 class="text-xl font-semibold text-gray-800">{{ 'home.feature1.title' | translate }}</h2>
      <p class="text-gray-600">{{ 'home.feature1.description' | translate }}</p>
    </div>
    <!-- Card 11 -->
    <div class="bg-white p-4 rounded shadow hover:shadow-lg transition duration-300">
      <h2 class="text-xl font-semibold text-gray-800">{{ 'home.feature2.title' | translate }}</h2>
      <p class="text-gray-600">{{ 'home.feature2.description' | translate }}</p>
    </div>
    <!-- Card 12 -->
    <div class="bg-white p-4 rounded shadow hover:shadow-lg transition duration-300">
      <h2 class="text-xl font-semibold text-gray-800">{{ 'home.feature3.title' | translate }}</h2>
      <p class="text-gray-600">{{ 'home.feature3.description' | translate }}</p>
    </div>
  </div>
</div>
