import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { ApiService } from './api.service';

interface User {
  id: number;
  email: string;
  name?: string;
  token?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject: BehaviorSubject<User | null>;
  public currentUser$: Observable<User | null>;
  private tokenKey = 'auth_token';

  constructor(
    private apiService: ApiService,
    private router: Router
  ) {
    // Initialize from localStorage on service creation
    const storedUser = this.getStoredUser();
    this.currentUserSubject = new BehaviorSubject<User | null>(storedUser);
    this.currentUser$ = this.currentUserSubject.asObservable();
  }

  private getStoredUser(): User | null {
    const token = localStorage.getItem(this.tokenKey);
    if (!token) return null;

    try {
      // In a real app, you might decode the JWT token to get user info
      // For now, we'll just create a simple user object
      return {
        id: 1,
        email: '<EMAIL>',
        token: token
      };
    } catch (error) {
      console.error('Error parsing stored user:', error);
      return null;
    }
  }

  public get currentUserValue(): User | null {
    return this.currentUserSubject.value;
  }

  public isAuthenticated(): boolean {
    return !!this.currentUserValue;
  }

  login(email: string, password: string): Promise<User> {
    // In a real app, this would call your API
    return this.apiService.post<{ user: User }>('/api/auth/login', { email, password })
      .then(response => {
        const user = response.data.user;

        // Store token in localStorage
        if (user && user.token) {
          localStorage.setItem(this.tokenKey, user.token);
          this.currentUserSubject.next(user);
        }

        return user;
      });
  }

  logout(): void {
    // Remove token from localStorage
    localStorage.removeItem(this.tokenKey);
    this.currentUserSubject.next(null);

    // Navigate to login page
    this.router.navigate(['/auth']);
  }

  // For demo purposes, this method simulates a login without API call
  // Remove this in production
  simulateLogin(): void {
    const mockUser: User = {
      id: 1,
      email: '<EMAIL>',
      name: 'Admin User',
      token: 'mock-jwt-token'
    };

    localStorage.setItem(this.tokenKey, mockUser.token!);
    this.currentUserSubject.next(mockUser);
  }
}
